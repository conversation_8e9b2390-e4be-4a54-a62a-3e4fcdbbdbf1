<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid py-4 px-4">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb px-3 py-2 rounded" style="background-color: var(--lighter-bg);">
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>" class="text-light-text"><i class="fas fa-home me-1"></i>Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/list') ?>" class="text-light-text"><i class="fas fa-building me-1"></i>Organizations</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="text-light-text"><?= esc($org['name']) ?></a></li>
            <li class="breadcrumb-item active" aria-current="page" style="color: var(--accent-color);">Edit Organization</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h3 class="mb-1 fw-bold text-light-text">
                <i class="fas fa-edit me-2 text-primary"></i>Edit Organization
            </h3>
            <p class="text-secondary mb-0">Update organization details for <?= esc($org['name']) ?></p>
        </div>
        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text">
            <i class="fas fa-arrow-left me-2"></i> Back to Organization
        </a>
    </div>

    <!-- Organization Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary py-3">
                    <h5 class="fw-bold mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>Edit Organization Details
                    </h5>
                </div>
                <div class="card-body p-4">
                    <?= form_open_multipart('dakoii/organization/update/' . $org['orgcode']) ?>

                    <div class="row g-4">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Basic Information</h6>

                            <div class="mb-3">
                                <label for="orgcode" class="form-label text-light-text fw-bold">Organization Code</label>
                                <input type="text" class="form-control bg-lighter-bg" name="orgcode" id="orgcode" value="<?= esc($org['orgcode']) ?>" readonly>
                            </div>

                            <div class="mb-3">
                                <label for="org_name" class="form-label text-light-text fw-bold">Organization Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="org_name" id="org_name" value="<?= esc($org['name']) ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label text-light-text fw-bold">Description</label>
                                <textarea class="form-control" name="description" id="description" rows="4"><?= esc($org['description']) ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="status" class="form-label text-light-text fw-bold">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="1" <?= $org['is_active'] == 1 ? 'selected' : '' ?>>Active</option>
                                    <option value="0" <?= $org['is_active'] == 0 ? 'selected' : '' ?>>Inactive</option>
                                </select>
                            </div>
                        </div>

                        <!-- Location and Logo -->
                        <div class="col-md-6">
                            <h6 class="text-primary fw-bold mb-3 border-bottom pb-2">Location Settings</h6>

                            <div class="mb-3">
                                <label for="country" class="form-label text-light-text fw-bold">Address Lock Country</label>
                                <select name="country" id="country" class="form-select">
                                    <option value="">Select Country</option>
                                    <option value="<?= $set_country['id'] ?>" <?= $org['location_lock_country'] == $set_country['id'] ? 'selected' : '' ?>>
                                        <?= esc($set_country['name']) ?>
                                    </option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="province" class="form-label text-light-text fw-bold">Address Lock Province</label>
                                <select name="province" id="province" class="form-select">
                                    <option value="">Select Province</option>
                                    <?php foreach ($get_provinces as $prov): ?>
                                        <option value="<?= $prov['id'] ?>"
                                                <?= $org['location_lock_province'] == $prov['id'] ? 'selected' : '' ?>>
                                            <?= esc($prov['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <h6 class="text-primary fw-bold mb-3 border-bottom pb-2 mt-4">Organization Logo</h6>

                            <div class="mb-3">
                                <label for="org_logo" class="form-label text-light-text fw-bold">Upload New Logo</label>
                                <input type="file" class="form-control" name="org_logo" id="org_logo" accept="image/*">
                                <div class="form-text text-secondary">Current logo will be replaced if new one is uploaded</div>
                            </div>

                            <?php if ($org['orglogo']): ?>
                            <div class="mt-2">
                                <div class="text-center p-3 bg-lighter-bg rounded">
                                    <img src="<?= imgcheck($org['orglogo']) ?>" alt="Current Logo" class="img-thumbnail" style="height: 100px; object-fit: contain;">
                                    <div class="mt-2 text-secondary small">Current Logo</div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <input type="hidden" name="id" value="<?= $org['id'] ?>">
                        <input type="hidden" name="orgcode" value="<?= $org['orgcode'] ?>">

                        <a href="<?= base_url('dakoii/organization/view/' . $org['orgcode']) ?>" class="btn btn-outline-secondary text-light-text me-md-2">
                            <i class="fas fa-times me-2"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary text-white">
                            <i class="fas fa-save me-2"></i> Update Organization
                        </button>
                    </div>

                    <?= form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
